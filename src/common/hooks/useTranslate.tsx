import { useEffect } from 'react'
import { message } from 'antd/es'
import { ModelType, MessageType } from '@src/common/const'
import { ipConnectivity } from '@src/common/utils'

export default () => {
  const [messageApi, contextHolder] = message.useMessage()

  const getUserId = () => {
    // 由于移除了登录功能，这里返回一个默认用户ID
    return "anonymous_user"
  }

  // 创建会话
  const createConversation = async (sendResponse) => {
    try {
      const response = await fetch(
        `http://*************:9607/ai/orchestration/session/createSession`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            UserID: getUserId(),
          }),
        }
      )

      const res = await response.json()
      const conversationID = res?.Conversation?.AppConversationID

      if (conversationID) {
        sendResponse({
          code: '0',
          conversationID: conversationID,
        })
      } else {
        sendResponse({
          code: '1',
          error: '会话创建失败',
        })
      }
    } catch (error) {
      console.log('创建会话错误:', error)
      sendResponse({
        code: '1',
        error: error.message,
      })
    }
  }

  // 处理翻译请求
  const handleTranslate = async (query, conversationID, sendResponse) => {
    try {
      const chatResponse = await fetch(
        `${ModelType.translate.value}/api/v1/chat_query`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            UserID: getUserId(),
            query: query,
            AppConversationID: conversationID,
            ResponseMode: 'blocking',
          }),
        }
      )
      const chatRes = await chatResponse.text()
      console.log('翻译响应:', {
        query,
        chatRes
      })

      try {
        // 参考Java代码的处理方式
        const lines = chatRes.split('\n');
        if (lines.length > 1) {
          // 假设第二行包含我们需要的数据
          const dataLine = lines[1];
          // 假设数据以某个前缀开始
          const prefix = 'data:data: ';
          if (dataLine.startsWith(prefix)) {
            const jsonStr = dataLine.substring(prefix.length);
            const jsonObj = JSON.parse(jsonStr);
            if (jsonObj && jsonObj.answer) {
              console.log('jsonStr:', jsonStr, jsonObj.answer);

              sendResponse({
                code: '0',
                result: jsonObj.answer.replace('翻译结果：', '').trim(),
              });
              return;
            }
          }
        }

        // 如果上面的处理失败，尝试使用正则表达式
        const regex = /翻译结果：(.*?)(?=[^\\]")/;
        const match = chatRes.match(regex);
        if (match) {
          const translation = match[1].trim();
          sendResponse({
            code: '0',
            result: translation,
          });
        } else {
          sendResponse({
            code: '1',
            error: '翻译结果解析失败',
          });
        }
      } catch (parseError) {
        console.log('解析翻译结果错误:', parseError);
        sendResponse({
          code: '1',
          error: '翻译结果解析失败: ' + parseError.message,
        });
      }
    } catch (error) {
      console.log('翻译错误:', error)
      sendResponse({
        code: '1',
        error: error.message,
      })
    }
  }

  useEffect(() => {
    ipConnectivity(messageApi)
    const messageHandler = (message, sender, sendResponse) => {
      if (message.type === MessageType.CREATE_CONVERSATION) {
        createConversation(sendResponse)
      } else if (message.type === MessageType.BATCH_TRANSLATE) {
        const { query, conversationID } = message.data
        handleTranslate(query, conversationID, sendResponse)
      } else if (message.type === MessageType.SINGLE_TRANSLATE) {
        const { query, conversationID } = message.data
        handleTranslate(query, conversationID, sendResponse)
      }
      return true
    }

    chrome.runtime.onMessage.addListener(messageHandler)

    // 清理函数
    return () => {
      chrome.runtime.onMessage.removeListener(messageHandler)
    }
  }, [])

  return contextHolder
}
