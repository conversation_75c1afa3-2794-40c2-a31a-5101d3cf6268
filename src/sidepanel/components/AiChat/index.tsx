import React, { useRef, lazy, Suspense, useEffect } from 'react'
import { init, log } from '@ht/xlog'
import { useAiChat } from '@src/common/hooks'
import { ModelType } from '@src/common/const'
import { isPrd } from '@src/common/utils'
import { message } from 'antd/es'
import { getCurrentTab } from '@src/sidepanel/utils'
import GuidePage from './GuidePage'
import AboveNode from './AboveNode'
import FooterVersion from './FooterVersion'
import Loading from './Loading'
import HistoryListItem from './HistoryListItem'
import '@ht/chatui/dist/index.css'
import './style.less'

const LazyChatUi = lazy(() => import('@ht/chatui'))

interface AiChatProps {
  selectedText?: string;
  textOperation?: string;
}

export default ({ selectedText, textOperation }: AiChatProps) => {
  const chatUiRef = useRef(null)
  const [messageApi, contextHolder] = message.useMessage()
  const { modelType, isPagesummary } = useAiChat()

  type TRenderWelcomeReturnType = React.ReactNode &
    React.ForwardRefExoticComponent<any>

  const onReportLog = (params) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }

  async function extractPageContent(currentTab: any): Promise<string> {
    return new Promise((resolve) => {
      chrome.scripting
        .executeScript({
          target: { tabId: currentTab.id },
          func: () => {
            // 非递归、深度优先遍历获取所有的dom元素
            const DFSDomTraversal = (root) => {
              if (!root) return

              const arr = []
              const queue = [root]
              let node = queue.shift()

              while (node) {
                arr.push(node)
                const childLen = node.children.length
                if (childLen) {
                  for (let i = childLen - 1; i >= 0; i--) {
                    queue.unshift(node.children[i])
                  }
                }
                node = queue.shift()
              }
              return arr
            }

            /**
             * 获取dom结构
             */
            function copyRootDomContent(root, copyRoot) {
              const rootDom = DFSDomTraversal(root)
              const copyRootDom = DFSDomTraversal(copyRoot)
              const removeElement = (originElement, eleIndex) => {
                const copyElement = copyRootDom[eleIndex]

                // 要过滤的标签列表
                const FILTER_TAGS = [
                  'canvas',
                  'svg',
                  'img',
                  'video',
                  'audio',
                  'iframe',
                  'embed',
                  'meta',
                  'link',
                  'script',
                  'style',
                  'hr',
                ]

                // 检查元素是否隐藏
                function isHidden(node) {
                  if (!(node instanceof Element)) return false

                  const style = window.getComputedStyle(node)
                  return (
                    style.display === 'none' ||
                    style.visibility === 'hidden' ||
                    style.opacity === '0' ||
                    node.hasAttribute('hidden')
                  )
                }

                if (
                  FILTER_TAGS.includes(originElement.tagName?.toLowerCase())
                ) {
                  copyElement.remove()
                } else if (isHidden(originElement)) {
                  copyElement.remove()
                }
              }

              rootDom.forEach(removeElement)
              return new XMLSerializer().serializeToString(copyRoot)
            }

            const copyHtmlContent = () => {
              console.log('--------------copyHtmlContent-------------------')
              console.log(document.title)
              const root = document.documentElement
              const cloneRoot = root.cloneNode(true)
              const content = copyRootDomContent(root, cloneRoot)

              return content.replace(/\s+/g, ' ')
            }
            return copyHtmlContent()
          },
        })
        .then((result) => {
          resolve(result[0].result)
        })
    })
  }

  const getPageContent = async () => {
    const currentTab = await getCurrentTab(messageApi)
    const htmlContent = await extractPageContent(currentTab)
    return htmlContent
  }

  const config = {
    //租户id：表示当前系统
    appId: 'web-assistant',

    //用户id：代表当前系统唯一用户id
    userId: 'anonymous_user',

    requests: {
      baseUrl() {
        return `aichrome`
      },


      //问答接口
      send: {
        url: '/chat/workflow/chrome',
        isAibag: true,
        stream: true,
        messageInterval: 50,
        async requestTransfer(params) {
          if (isPagesummary) {
            params.inputs = {
              HTML_CONTENT: await getPageContent(),
            }
          }
          return params
        },
      },
      //查询历史详情接口
      history: {
        url: '/ai/orchestration/session/getHistoryMessages',
      },
      //点赞点踩接口
      score: {
        url: '/ai/orchestration/session/feedback',
      },
      //停止生成接口
      stop: {
        url: '/ai/orchestration/session/interruptSession',
      },

      // 历史会话列表
      historyConversation: {
        url: '/ai/orchestration/session/getHistorySessions',
      },

      deleteHistoryConversation: {
        url: '/ai/orchestration/session/deleteSession',
      },

      deleteMessage: {
        url: '/ai/orchestration/session/deleteMessage',
      },

    },
  }

  const getPlaceholder = () => {
    return (
      Object.values(ModelType).find((item) => item.value === modelType)
        ?.placeholder || '请输入您的问题'
    )
  }

  // 初始化日志
  const initLog = () => {
    init({
      uuid: 'anonymous_user',
      from: 'HtscAiExtension',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        // 项目信息配置，数据是上报到数智中台，需要去数智中台申请一个项目（product_id和product_name）
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test', // 上报环境
      },
    })
  }

  useEffect(() => {
    initLog()
  }, [])

  // 处理继续问的情况
  useEffect(() => {
    if (selectedText && textOperation === '继续问') {
      // 当是继续问时，自动发送消息
      const chatContext = chatUiRef.current?.chatContext;
      if (chatContext && chatContext.sendMessage) {
        // 延迟一下确保组件完全加载
        setTimeout(() => {
          chatContext.sendMessage(selectedText);
        }, 100);
      }
    }
  }, [selectedText, textOperation])

  const composerConfig = {
    // aboveNode: <AboveNode chatUiRef={chatUiRef} />,
    placeholder: getPlaceholder(),
    quoteOperations: {
      citetext: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译翻译',// 发送时的命令 --》 拼装
        agentId: 'agent1',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],
      citeweb: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译翻译',// 发送时的命令 --》 拼装
        agentId: 'agent1',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],

      image: [
        {
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '翻译',// 文案
          question: '翻译翻译',// 发送时的命令 --》 拼装
          agentId: 'agent1',//发送时可指定智能体ID --》拼装
          customRender: '',//自定义渲染
        }
      ],

      file: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译翻译',// 发送时的命令 --》 拼装
        agentId: 'agent1',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],
    },
    skill: [
      {
        key: 'translate',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '智能翻译',// 文案
        question: '翻译上面文字',// 发送时的命令
        agentId: '1',//发送时可指定智能体ID
        children: [
          {
            key: 'translate',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: 'AI翻译',// 文案
            question: 'AI翻译',// 发送时的命令
            agentId: 'translate',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          },
          {
            key: 'translatePage',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '翻译此页面',// 文案
            question: '翻译此页面',// 发送时的命令
            agentId: 'translate',//发送时可指定智能体ID
            customRender: '',//自定义渲染
            onClick: () => {
              
            },
          }
        ],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      },
      {
        key: 'extract',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '网页总结',// 文案
        question: '网页总结',// 发送时的命令
        agentId: 'extract',//发送时可指定智能体ID
        customRender: '',//自定义渲染
        onClick: () => console.log('extract clicked'),
      },
      {
        key: 'more',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '更多',// 文案
        question: '',// 发送时的命令
        agentId: '',//发送时可指定智能体ID
        children: [
          {
            key: 'abbreviation',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '缩写',// 文案
            question: '缩写',// 发送时的命令
            agentId: 'abbreviation',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          },
          {
            key: 'expand',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '扩写',// 文案
            question: '扩写',// 发送时的命令
            agentId: 'expand',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          },
          {
            key: 'retouch',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '改写',// 文案
            question: '改写',// 发送时的命令
            agentId: 'retouch',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          },
          {
            key: 'amend',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '修订语法与拼写',// 文案
            question: '修订语法与拼写',// 发送时的命令
            agentId: 'amend',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          }
        ],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log('more clicked'),// 技能点击回调 -》 由AiChat统一写入Composer
      }
    ],

  }

  return (
    <Suspense fallback={<Loading />}>
      <LazyChatUi
        navbar={{
          showLogo: false,
          showCloseButton: false,
          title: '',
        }}
        ref={chatUiRef}
        config={config}
        historyConversation={{
          renderListItem(props) {
            return <HistoryListItem {...props} />
          },
        }}
        renderWelcome={(props) =>
          (<GuidePage {...props} />) as TRenderWelcomeReturnType
        }
        onReportLog={onReportLog}
        inputOptions={{
          minRows: 2,
        }}
        composerConfig={composerConfig}
        renderFooterVersion={() => <FooterVersion />}
        showStopAnswer={true}
        showToken={false} // 不展示消耗的token数量
        showHallucination={false} // 不展示合规话术
      />
      {contextHolder}
    </Suspense>
  )
}
